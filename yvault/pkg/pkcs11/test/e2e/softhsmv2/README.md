# SoftHSMv2 Build Scripts for PKCS#11 Testing

This directory contains cross-platform build scripts for compiling SoftHSMv2 from source code specifically for PKCS#11 testing in the yvault project.

## Files

- `install-softhsmv2.sh` - Bash build script for Linux/macOS/Windows (via MSYS2)
- `build_softhsm.ps1` - PowerShell build script for Windows
- `softhsm2.conf` - SoftHSMv2 configuration file
- `README.md` - This documentation file

## Quick Start

### Linux/macOS
```bash
chmod +x install-softhsmv2.sh
./install-softhsmv2.sh
source build/setup_env.sh
```

### Windows (PowerShell)
```powershell
.\build_softhsm.ps1
. .\build\setup_env.ps1
```

### Windows (MSYS2/Git Bash)
```bash
./install-softhsmv2.sh
source build/setup_env.sh
```

## Features

- **Cross-platform support**: Linux, macOS, and Windows
- **Automatic dependency checking**: Verifies required build tools
- **Parallel compilation**: Uses all available CPU cores
- **Self-contained installation**: Installs to local `build` directory
- **Pre-configured for testing**: Ready for PKCS#11 testing

## Prerequisites

### Linux (Ubuntu/Debian)
```bash
sudo apt-get install build-essential autoconf automake libtool pkg-config libssl-dev
```

### Linux (CentOS/RHEL/Fedora)
```bash
sudo dnf install gcc gcc-c++ make autoconf automake libtool pkgconfig openssl-devel
```

### macOS
```bash
brew install autoconf automake libtool pkg-config openssl
```

### Windows
Install MSYS2 from https://www.msys2.org/ and run:
```bash
pacman -S base-devel mingw-w64-x86_64-toolchain mingw-w64-x86_64-autotools mingw-w64-x86_64-openssl
```

## Build Scripts

### Bash Script (`install-softhsmv2.sh`)
- Downloads SoftHSMv2 v2.6.1 from GitHub
- Configures with ECC, GOST, and EdDSA support
- Creates setup scripts for environment configuration
- Supports Linux, macOS, and Windows (MSYS2)

## Configuration

The `softhsm2.conf` file configures SoftHSMv2 for testing:
- Token directory: `./test_data/`
- File-based object store
- INFO-level logging
- Non-removable slots

## Usage After Build

1. **Initialize a test token**:
   ```bash
   softhsm2-util --init-token --slot 0 --label "TestToken"
   ```

2. **List available slots**:
   ```bash
   softhsm2-util --show-slots
   ```

3. **Use in Go PKCS#11 tests**:
   - Library path: `build/lib/softhsm/libsofthsm2.so` (Unix) or `build/lib/softhsm/libsofthsm2.dll` (Windows)
   - Configuration: `softhsm2.conf`
   - Token directory: `test_data/`

## Directory Structure After Build

```
softhsmv2/
├── build/                  # Installation directory
│   ├── bin/               # SoftHSMv2 binaries
│   ├── lib/               # PKCS#11 libraries
│   └── setup_env.sh       # Environment setup script
├── test_data/             # Token storage directory
└── softhsm2.conf         # Configuration file
```

## Integration with yvault

The scripts configure environment variables for seamless integration:
- `SOFTHSM2_CONF`: Points to configuration file
- `PATH`: Includes SoftHSMv2 binaries
- `LD_LIBRARY_PATH`: Includes library directory (Unix)

Source the setup script before running yvault PKCS#11 tests.

