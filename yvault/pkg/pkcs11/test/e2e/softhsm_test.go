package e2e

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/qcu266/labs/yvault/pkg/pkcs11"
)

// SoftHSMTestConfig holds configuration for SoftHSM testing
type SoftHSMTestConfig struct {
	TokenDir   string
	TokenLabel string
	UserPIN    string
	SOPIN      string
	SlotID     uint
}

// NewSoftHSMTestConfig creates a default SoftHSM test configuration
func NewSoftHSMTestConfig() *SoftHSMTestConfig {
	return &SoftHSMTestConfig{
		TokenDir:   filepath.Join(os.TempDir(), fmt.Sprintf("softhsm-test-%d", time.Now().UnixNano())),
		TokenLabel: "TestToken",
		UserPIN:    "1234",
		SOPIN:      "5678",
		SlotID:     0,
	}
}

// SetupSoftHSM initializes a SoftHSM token for testing
func SetupSoftHSM(t *testing.T) (*pkcs11.Config, func()) {
	t.Helper()

	// Check if we have a bundled SoftHSM library
	libraryPath, err := getBundledSoftHSMPath()
	if err != nil {
		t.Fatalf("SoftHSM library is required but not available: %v\n\nInstall SoftHSM with:\n softhsmv2/install-softhsmv2.sh \n\nOr set PKCS11_LIBRARY_PATH to an existing SoftHSM library path.", err)
	}

	// Create test configuration
	testConfig := NewSoftHSMTestConfig()

	// Create token directory
	err = os.MkdirAll(testConfig.TokenDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create token directory: %v", err)
	}

	// Set up SoftHSM configuration
	configPath := filepath.Join(testConfig.TokenDir, "softhsm.conf")
	configContent := fmt.Sprintf(`
# SoftHSM v2 configuration file for testing
directories.tokendir = %s
objectstore.backend = file
log.level = ERROR
`, testConfig.TokenDir)

	err = os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write SoftHSM config: %v", err)
	}

	// Set environment variables for SoftHSM
	originalSoftHSMConf := os.Getenv("SOFTHSM2_CONF")
	os.Setenv("SOFTHSM2_CONF", configPath)

	// Initialize token using softhsm2-util if available
	if err := initializeSoftHSMToken(testConfig); err != nil {
		t.Logf("Failed to initialize token with softhsm2-util: %v", err)
		t.Log("Continuing with manual token initialization")
	}

	// Create PKCS#11 config
	pkcs11Config := &pkcs11.Config{
		LibraryPath: libraryPath,
		SlotID:      testConfig.SlotID,
		UserPIN:     testConfig.UserPIN,
	}

	// Cleanup function
	cleanup := func() {
		// Restore original environment
		if originalSoftHSMConf != "" {
			os.Setenv("SOFTHSM2_CONF", originalSoftHSMConf)
		} else {
			os.Unsetenv("SOFTHSM2_CONF")
		}

		// Remove test directory
		os.RemoveAll(testConfig.TokenDir)
	}

	return pkcs11Config, cleanup
}

// initializeSoftHSMToken attempts to initialize a SoftHSM token using softhsm2-util
func initializeSoftHSMToken(config *SoftHSMTestConfig) error {
	// Try to find softhsm2-util
	utilPath, err := exec.LookPath("softhsm2-util")
	if err != nil {
		return fmt.Errorf("softhsm2-util not found: %w", err)
	}

	// Initialize token
	cmd := exec.Command(utilPath,
		"--init-token",
		"--slot", fmt.Sprintf("%d", config.SlotID),
		"--label", config.TokenLabel,
		"--so-pin", config.SOPIN,
		"--pin", config.UserPIN)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to initialize token: %w, output: %s", err, output)
	}

	return nil
}

// IsSoftHSMAvailable checks if SoftHSM is available for testing
func IsSoftHSMAvailable() bool {
	_, err := getBundledSoftHSMPath()
	return err == nil
}

// getBundledSoftHSMPath returns the path to the bundled SoftHSM library for the current platform.
func getBundledSoftHSMPath() (string, error) {
	// Get current file's directory to locate the lib directory
	_, currentFile, _, ok := runtime.Caller(0)
	if !ok {
		return "", errors.New("could not determine current file path")
	}

	// Navigate to ./lib from e2e_test directory
	e2eDir := filepath.Dir(currentFile)
	libDir := filepath.Join(e2eDir, "softhsmv2", "build", "lib", "softhsm")

	// Construct library path
	libPath := filepath.Join(libDir, "libsofthsm2.so")

	// Check if file exists
	if _, err := os.Stat(libPath); os.IsNotExist(err) {
		return "", errors.Errorf("bundled SoftHSM library not found at: %s", libPath)
	}

	return libPath, nil
}

// RequireSoftHSM fails the test if SoftHSM is not available (SoftHSM is mandatory)
func RequireSoftHSM(t *testing.T) {
	t.Helper()
	if !IsSoftHSMAvailable() {
		platform := runtime.GOOS + "-" + runtime.GOARCH
		t.Fatalf("SoftHSM is required but not available for platform %s.\n\nInstall SoftHSM with:\n  ./scripts/install-softhsm.sh\n\nSoftHSM is a software-based HSM that should work on all platforms.", platform)
	}
}

// SkipIfSoftHSMUnavailable is deprecated, use RequireSoftHSM instead
// TODO: Remove this function after updating all test files
func SkipIfSoftHSMUnavailable(t *testing.T) {
	t.Helper()
	RequireSoftHSM(t)
}
